"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _Avatar = _interopRequireDefault(require("./Avatar"));
var _AvatarGroup = _interopRequireDefault(require("./AvatarGroup"));
const Avatar = _Avatar.default;
Avatar.Group = _AvatarGroup.default;
var _default = exports.default = Avatar;