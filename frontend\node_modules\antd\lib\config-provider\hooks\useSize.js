"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = _interopRequireDefault(require("react"));
var _SizeContext = _interopRequireDefault(require("../SizeContext"));
const useSize = customSize => {
  const size = _react.default.useContext(_SizeContext.default);
  const mergedSize = _react.default.useMemo(() => {
    if (!customSize) {
      return size;
    }
    if (typeof customSize === 'string') {
      return customSize !== null && customSize !== void 0 ? customSize : size;
    }
    if (typeof customSize === 'function') {
      return customSize(size);
    }
    return size;
  }, [customSize, size]);
  return mergedSize;
};
var _default = exports.default = useSize;