"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _vi_VN = _interopRequireDefault(require("rc-picker/lib/locale/vi_VN"));
var _vi_VN2 = _interopRequireDefault(require("../../time-picker/locale/vi_VN"));
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: 'Chọn thời điểm',
    yearPlaceholder: 'Chọn năm',
    quarterPlaceholder: 'Chọn quý',
    monthPlaceholder: 'Chọn tháng',
    weekPlaceholder: 'Chọn tuần',
    rangePlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeQuarterPlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeMonthPlaceholder: ['<PERSON>h<PERSON>g bắt đầu', 'Th<PERSON>g kết thúc'],
    rangeWeekPlaceholder: ['Tuần bắt đầu', 'Tuần kết thúc']
  }, _vi_VN.default),
  timePickerLocale: Object.assign({}, _vi_VN2.default)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
var _default = exports.default = locale;